📝 PRD: LinkedIn Repurposing Format for SEO Article
Title: LinkedIn Repurposing Strategy – "Turn Data into Your Profit" Authority Content
 Target Platform: LinkedIn
 Purpose: Convert a long-form SEO article into multiple LinkedIn-native posts that drive engagement, position the brand as a data-to-profit authority, and spark conversation.

1. 🎯 Goals
Drive engagement (likes, comments, shares).


Build brand authority around “Turn Data into Your Profit.”


Get inbound interest (DMs, connections, leads).


Boost SEO article visibility through indirect interest.


Train LinkedIn algorithm to prioritize content visibility for decision-makers in agriculture, data, and enterprise sectors.



2. 👤 Target Audience
Estate owners & plantation managers (agriculture)


Tech-aware founders & COOs


Data-driven executives


Grant providers (like Cradle, CIP Spark)


Journalists or LinkedIn creators in agri-tech / IoT / AI


Professionals looking to replicate success in other industries



3. 📦 Content Output Format
A. Multi-Post Breakdown (Pillar ➝ Micro)
Repurpose a single SEO article into 5–7 LinkedIn-native micro-posts, each focusing on one idea or angle:
Post Type
Format
Hook Style
CTA
1. Personal Insight
Text-only
"We almost gave up until this one number changed everything."
“What’s one KPI that changed your business?”
2. Industry Take
Text + Chart or Slide
"Why traditional soil testing is failing estate owners"
“Do you agree?”
3. Case Study Highlight
Slide Carousel
“How we helped [X] reduce fertilizer waste by 30%”
“Want the full report?”
4. Thought-Leadership
Text-only
“Data is NOT the new oil. It’s the new fertilizer.”
“Let’s debate.”
5. Framework or Process
Carousel or Text
“Here’s our exact method to turn soil data into ROI”
“Which step do you use?”
6. Hiring/Team/Behind-the-Scenes
Text + Image
“Building this product took 6 failed versions. Here’s what stuck.”
“We’re growing – let’s connect.”
7. Hook for full article
Text + Comment Link
“Here’s the long version if you want to dive deep👇”
“(Link in comment)”


4. ✍️ Writing Style Guide
Tone: Conversational, confident, humble where needed


Format: Short paragraphs, heavy line breaks


Length: 200–300 words per post


Start strong: First 2 lines must stop the scroll


Include a question or CTA at the end


Visuals: Charts, diagrams, or carousels (where possible)


Avoid: Hashtag spam, too many links, overly corporate tone



5. 🧠 Content Development Process
For AI Agent (Augment Code) Guidance:
a. MCP Usage Guidelines
sequential_thinking: Break the SEO article into clearly defined subtopics and order them based on emotional and logical weight.


playwright: Scrape top-performing LinkedIn posts in similar niches to extract trends, visual styles, and hooks.


seo-mcp: Track which LinkedIn posts lead to off-platform clicks or mentions in Google-indexed results.



6. ✅ KPIs to Track
💬 Comment Rate


🤝 Connection Requests after viewing


🔗 Article Clicks (if link used)


📥 Inbound DMs / Inquiries


🔁 Post Shares


👁 Reach / Impressions per post



7. 🛠 Tools
Canva / Figma for carousel/slide creation


Shield App (optional) for LinkedIn analytics


ChatGPT + SEO-MCP + Playwright for repurposing + testing hooks


Google Sheets for post calendar & performance tracking



8. 📅 Sample Post Calendar
Date
Post Theme
Format
Mon
Story of failure ➝ ROI
Text
Wed
Data vs Traditional Farming
Carousel
Fri
Framework / 3-Step Method
Text or Slide
Next Mon
Personal insight / Lesson
Text
Next Wed
Invite to read full SEO article
Text + comment link



