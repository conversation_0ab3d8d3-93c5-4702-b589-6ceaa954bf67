📝 PRD: Repurposing SEO Article for Facebook
Product Name:
Facebook Content Adaptation – "Turn Data Into Your Profit" Series

1. 🎯 Purpose & Objective
To repurpose existing long-form SEO articles into Facebook-native content that drives engagement (likes, shares, comments, saves), brand authority, and click-through (where applicable), using a combination of:
Scroll-stopping text post formats (questions, stories, punchlines)


Visually supported formats (quote cards, carousels, infographics)


Platform-specific CTA



2. 👤 Target Audience
Same as your SEO target, but tailored to the mindset of Facebook users:
SME owners


Farmers, estate managers, and agritech enthusiasts


Data-driven decision-makers


General audience curious about "how to turn data into profit"



3. 🧠 Content Strategy
a. Core Message Adaptation
Preserve the authority and insight from the article


Translate to relatable, skimmable chunks fit for mobile scrolling


Use narrative hooks, storytelling, and CTA to drive discussion


b. Content Types to Create
Type
Format
Notes
Main text post
Plain text (≤ 150 words)
Start with strong hook + CTA
Punchy quote card
Image + text
Use short, emotional or shocking takeaway
Carousel (multi-image)
Series of visual slides
Simplify key ideas into 4–6 slides
One-question text post
Text only
Use open-ended “What do you think?” type
Fill-in-the-blank prompt
Text only
e.g. “The one data mistake I regret is ___”
Case study summary
Text + image
Short story with real problem > solution
Meme format (optional)
Meme + short caption
If tone allows, use smart memes to grab reach


4. ✍️ Writing Style & Tone
Conversational, not academic


Tap into frustrations, “aha” moments, or common myths


Use first-person or “you” language to create intimacy


Include emojis, line breaks, and tag questions (e.g. “Ever been there?”)


Highlight benefit and action (e.g., “This tip saved me RM15,000. Want it?”)



5. 📣 Engagement Mechanisms
Each Facebook version should encourage one or more:
Reactions (ask for LIKE if they agree)


Comments (ask “What’s your take?” or “Ever experienced this?”)


Shares (“Tag someone who needs this” / “Share if you’ve done this wrong”)


Saves (for tips/steps posts: “Save this for later”)



6. 🔗 Link Handling
Links only in first comment (avoid link in main caption unless for ads)


Posts should stand alone, with value even if no click happens


Offer mini value upfront → tease more in article link



7. 🧪 Testing & Variants
For each article repurpose:
Create 3 variations of the core message:


1 pure text


1 quote or visual


1 with carousel or storytelling format


Use A/B test in organic or paid boost (different headlines or CTAs)



8. 📅 Publishing Plan
Day
Content Type
Goal
Mon
Text post + question
Comments
Wed
Carousel tips
Saves
Fri
Quote card or meme
Shares
Sun
Story post or case study
Emotional reaction


9. 🧠 MCP Agent Usage Guidelines
Let your AI agent (Augment or similar) apply these:
a. sequential_thinking
Break SEO article into sections → prioritize most emotional/relatable parts


Convert each into a Facebook-friendly hook or story


Plan visual + text combination for each post


b. playwright
Crawl high-engagement Facebook pages for similar content


Analyze wording, emoji usage, hook type, visual formats used


Benchmark: pages like Neil Patel, HBR, DataBox, Sprout Social


c. seo-mcp
Use SEO keywords as hashtags or phrases in captions


Extract high-volume keyword phrases and turn into FB post ideas (e.g. “Why fertilizer cost keeps rising 📈”)



10. ✅ Success Metrics (KPI)
Metric
Target
Engagement rate per post
>5%
Comments per post
≥10
Shares per week
≥20
Link clicks (where relevant)
≥100/month
Saved posts
>30 saves/post (for tip carousels)


11. 🚫 What to Avoid
Wall-of-text paragraphs


Overly technical jargon without analogy


Link overload in caption


Passive tone or “just informative” style



✅ Summary Checklist for Each Post
Clear & short hook (first 3 lines = scroll stop)


One strong message or story


One clear CTA (comment/share/save/etc.)


Optional: image, visual, quote card


Optional: link in comment


